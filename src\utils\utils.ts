import { getDeviceInfo, formatDate } from '@/utils/common';
import http from '@/api/http';
import { refreshToken, getToken } from '@/plugins/app-plugin';
import { aiModal } from '@/plugins/ai-login-modal';
import { CardContentType } from '@/types/api';
import {ICON_MAP, TYPE_ARR, APP_NAME_MAP} from '@/const/const';
import API from '@/api/index';

type UtilsType = {
  setStreamString: (context: string, dataItem: any, index?: number) => any[],
  parseString: (input: string, reason_input?: string) => any[],
  getNewToken: (callback: any, isGetToken?: boolean) => void,
  formatContent: (context: string, dataList: any) => any[],
  formatHistoryContent: (context: string, citationsJson:string, type:number, needMarkBlueRunSteps: Array<any>, dataList: Array<any>) => any[],
  handleStreamMessage: (data:any, context: any) => any,
  dealKnowledgeData: (data:string) => any[],
  dealMarkerData: (data:string) => any[],
  // 2. 动态属性签名（允许任意字符串属性）
  [key: string]: (...args: any[]) => any;
}
//供handleStreamMessage使用
function dealCitationsJson(citationsJson: string, dataHistory:any[]) {
  if(typeof citationsJson === 'string' && citationsJson) {
    try {
      citationsJson = JSON.parse(citationsJson)
    } catch (error) {
      console.log('error', error);
    }
  }
  if(citationsJson?.length) {
    const lastDataItem = dataHistory[dataHistory.length - 1];
    lastDataItem.finish = 1;
    dataHistory.push({
      index: dataHistory.length,
      isKnowledgeData: true,
      context: citationsJson,
    });
  }
}
function dealFileData (data: Record<string, any[]>){ //对本地文件去重
  const konwledgeKeys = Object.keys(data);
  const localFilesObj: Record<string, any> = {};
  for(let i = 0 , len = konwledgeKeys.length; i < len; i++) {
    const key = konwledgeKeys[i];
    if(key === '0') {
      const localFiles = data[key];
      localFiles.forEach((item) => {
        if(!localFilesObj[item.resourceId]) {
          localFilesObj[item.resourceId] = item;
        }else { //重复文件
          if(item.similarity > localFilesObj[item.resourceId].similarity) { //取大的相似度
            localFilesObj[item.resourceId] = item;
          }
        }
      });
      data[key] = Object.values(localFilesObj);
      break;
    }
  }
  return data;
}
const utils: UtilsType = {
  setStreamString: function (context, dataItem, index) {
    let items = [dataItem];
    if (context) {
      let strBuffer = dataItem.buffer || '';
      let addStr = '';
      if (dataItem.isCard) {
        let tmpContext = dataItem.context + context;
        if (tmpContext.indexOf('</aicard>') >= 0) {
          let strBufferList = tmpContext.split('</aicard>');
          // 把前面的文本添加到context中
          let afterContext = strBufferList.shift();
          dataItem.context = afterContext;
          dataItem.buffer = '';
          try {
            dataItem.json = JSON.parse(afterContext || '{}');
          } catch (error) {
            dataItem.json = {};
          }
          // 结束这个item
          dataItem.finish = 1;
          // 后面的文本继续递归处理
          if (strBufferList.length > 0) {
            let afterContext = strBufferList.join('</aicard>');
            let newDataItem = {
              index: dataItem.index + 1,
              context: '',
            };
            let items = utils.setStreamString(afterContext, newDataItem);
            return [dataItem, ...items];
          }
        } else {
          dataItem.context = tmpContext;
        }
      } else {
        if (strBuffer) {
          strBuffer += context;
          if (strBuffer.startsWith('<aicard>')) {
            // 终止，并且返回一个dataItem,并且形成一个新的dataItem
            strBuffer = strBuffer.slice(8);
            if (!dataItem.context) {
              dataItem.isCard = true;
              let items = utils.setStreamString(strBuffer, dataItem);
              return items;
            } else {
              let newDataItem = {
                index: dataItem.index + 1,
                isCard: true,
                context: '',
              };
              dataItem.buffer = '';
              dataItem.finish = 1;
              let items = utils.setStreamString(strBuffer, newDataItem);
              return [dataItem, ...items];
            }
          } else {
            // 检查strBuffer
            let tmpStr8 = strBuffer.slice(0, 8);
            if ('<aicard>'.indexOf(tmpStr8) === 0) {
              dataItem.buffer = strBuffer;
            } else {
              dataItem.context = dataItem.context + strBuffer;
              dataItem.buffer = '';
            }
            return items;
          }
        }
        let strList = context.split('<');
        if (strList.length > 1) {
          addStr = strList[0];
          for (let i = 1; i < strList.length; i++) {
            let tmpStr = '<' + strList[i];
            let tmpStr8 = tmpStr.slice(0, 8);
            if ('<aicard>'.indexOf(tmpStr8) === 0) {
              strBuffer += tmpStr;
              dataItem.buffer = strBuffer;
            } else {
              addStr += tmpStr;
              dataItem.buffer = '';
            }
          }
        } else {
          addStr = context;
        }
        dataItem.context += addStr;
      }
    }
    return items;
  },
  parseString: function (input, reason_input) {
    const result = [];
    const regexes = [
      { type: 'iframe', regex: /<iframe.*?<\/iframe>/gs },
      { type: 'aicard', regex: /<aicard>(.*?)<\/aicard>/gs },
    ];

    let lastIndex = 0;
    let matchFound = false;

    // 处理不同类型的标签
    for (const { type, regex } of regexes) {
      let match: any;
      while ((match = regex.exec(input)) !== null) {
        matchFound = true;

        // 如果有匹配到的部分之前的文本
        if (match.index > lastIndex) {
          result.push({
            type: 'text',
            content: input.slice(lastIndex, match.index),
            reasoning_content: reason_input,
          });
        }

        // 匹配到的标签部分
        result.push({
          type,
          content: type === 'iframe' ? match[0] : match[1],
          reasoning_content: reason_input,
        });

        // 更新 lastIndex 为当前匹配结束的位置
        lastIndex = regex.lastIndex;
      }

      // 如果匹配到某种标签，停止进一步处理
      if (matchFound) {
        // cardType.value = type;
        break;
      }
    }

    // 如果没有任何标签匹配到，只处理文本
    if (!matchFound) {
      result.push({ type: 'text', content: input, reasoning_content: reason_input });
    } else {
      // 如果最后还有剩余的文本
      if (lastIndex < input.length) {
        result.push({
          type: 'text',
          content: input.slice(lastIndex),
          reasoning_content: reason_input,
        });
      }
    }
    return result;
  },
  formatHistoryContent: function(content: string, citationsJson: string, type:number, needMarkBlueRunSteps: Array<any>, dataList: Array<any>) {
    let resultList = [...dataList];
    let needMarkBlueRunStepsArray: any[] = [];
    if (needMarkBlueRunSteps.length) {
      needMarkBlueRunSteps.forEach((item, index) => {
        let data = item.content
        let result = utils.dealMarkerData(data);
        needMarkBlueRunStepsArray = needMarkBlueRunStepsArray.concat(result);
      })
    }
    if (content.startsWith("<iframe")) {
      resultList.push({
        index: resultList.length,
        isIframe: true,
        finish: 1,
        context: content
      });
    } else {
      let list = utils.parseString(content);
      list.forEach((el: { type: string; content: string; citationsJson?:string }) => {
        if (el.type == "aicard" && el.content) {
          resultList.push({
            isCard: true,
            index: resultList.length,
            finish: 1,
            context: el.content,
            json: JSON.parse(el.content)
          });
        } else if (el.type == "iframe") {
          resultList.push({
            index: resultList.length,
            isIframe: true,
            finish: 1,
            context: el.content
          });
        } else {
          resultList.push({
            index: resultList.length,
            finish: 1,
            context: el.content,
            tagData: needMarkBlueRunStepsArray
          });
        }
      });
    }

    if(type === 0) {
      if(typeof citationsJson === 'string' && citationsJson) {
        try {
          citationsJson = JSON.parse(citationsJson)
        } catch (error) {
          console.log('error', error);
        }
      }
      if(citationsJson?.length) {
        resultList[resultList.length - 1].citations = citationsJson[0];
      }

    }else{
      if(typeof citationsJson === 'string' && citationsJson) {
        try {
          citationsJson = JSON.parse(citationsJson)
        } catch (error) {
          console.log('error', error);
        }
      }
      if(citationsJson?.length) {
        resultList.push({
          index: resultList.length,
          isKnowledgeData: true,
          context: citationsJson,
        });
      }
    }
    return resultList;
  },
  formatContent: function(content: string, dataList: Array<any>) {
    let resultList = [...dataList];
    if (content.startsWith("<iframe")) {
      resultList.push({
        index: resultList.length,
        isIframe: true,
        finish: 1,
        context: content
      });
    } else {
      let list = utils.parseString(content);
      list.forEach((el: { type: string; content: string; citationsJson?:string }) => {
        if (el.type == "aicard" && el.content) {
          resultList.push({
            isCard: true,
            index: resultList.length,
            finish: 1,
            context: el.content,
            json: JSON.parse(el.content)
          });
        } else if (el.type == "iframe") {
          resultList.push({
            index: resultList.length,
            isIframe: true,
            finish: 1,
            context: el.content
          });
        } else {
          resultList.push({
            index: resultList.length,
            finish: 1,
            context: el.content
          });
        }
      });
    }
    return resultList;
  },
  getNewToken: function (callback, isGetToken) {
    const { isApp } = getDeviceInfo();
    let tokenFn = refreshToken;
    if (isGetToken) {
      tokenFn = getToken;
    }
    if (isApp) {
      tokenFn({}, (data: string) => {
        const receiveData = JSON.parse(data) || {};
        localStorage.setItem('tokenStr', receiveData.accessToken);
        callback && callback();
      });
      return;
    } else if(!localStorage.getItem('tokenStr') || !isGetToken){
      aiModal({
        onOk: (value: any) => {
          const params = {
            clientUid: String(Math.floor(Math.random() * 1000000)),
            // loginName: 'wt', //需要根据实际的用户名密码
            // password: '123456',
            terminal: 'androidphone',
            loginType: 'username',
            ...value,
          };
          const url = '/api/auth/login';
          http.post(url, params, {}).then((data: any) => {
            if (data?.code === 0) {
              const result = data.data;
              localStorage.setItem('tokenStr', result?.doubleToken?.accessToken);
              API.store.action.setState('tokenStr', result?.doubleToken?.accessToken);
              callback && callback();
            }
          });
        },
        onCancel: () => {
          console.log('cancel');
        },
      });
    }else {
      callback && callback();
    }
  },
  dealKnowledgeData(data:string) {
    if(!data || data === '{}') return [];
    const parseData = JSON.parse(data);
    const result = dealFileData(parseData);
    const appMap: any = {}, showArray:any=[];
    const allKnowledgeItems: any[] = [];
    const cultureBuild = ['7', '8', '9', '10']; //文化建设
    const timeSchedule = ['94', '6', '11', '30', '5']; //时间安排
    Object.entries(result).forEach(([sourceKey, sourceValue])=>{
      let key = sourceKey;
      const knowledgeValue = (sourceValue as Array<any>).map((value)=>{

        const docType = value.title.split('.').pop();
        return {
          ...value,
          iconClass: ICON_MAP[sourceKey as keyof typeof ICON_MAP] || ICON_MAP[docType as keyof typeof ICON_MAP] || 'docIconfont moren-xin',
          similarity: value.similarity.toFixed(4),
          createDate:  formatDate(Number(value.createDate)).formatResult,
        }
      });

      if(cultureBuild.includes(key)) { //文化建设
        key = 'cultureBuild';
      }else if(timeSchedule.includes(key)) {
        key = 'timeSchedule';
      }
      //文化建设类型的需要合并
      if(!appMap[key]) {
        appMap[key] = {
          label: APP_NAME_MAP[key],
          id: key,
          knowledgeArr: [],
        };
      }
      appMap[key].knowledgeArr.push(...knowledgeValue);
      allKnowledgeItems.push(...knowledgeValue);
    });
    const allItem = {
      label: '全部',
      id: 'all',
      knowledgeArr: allKnowledgeItems
    }
    appMap['all'] = allItem;
    TYPE_ARR.map((item)=>{
      if(appMap[item]) {
        const { knowledgeArr } = appMap[item];
        showArray.push({
          ...appMap[item],
          number: knowledgeArr.length,
          knowledgeArr: knowledgeArr.sort((a:{similarity:number},b:{similarity:number})=>(b.similarity - a.similarity)),
        })
      }
    });
    return showArray;
  },
  dealMarkerData(data:string) {
    if(!data || data === '{}') return [];
    const result = JSON.parse(data);
    return result;
  },
  /**
   * 处理流式消息的公共方法
   */
  handleStreamMessage(
    data: any,
    context: {
      isFirst: boolean;
      lastDataItem: CardContentType;
      dataHistory: CardContentType[];
      messageType: Record<number, boolean>;
      recommandQuestion: Array<{ index: number; subject: string }>;
      needMarkBlueRunStepsArray: Array<any>;
      option: any;
      isStop: boolean;
      isCancel: boolean;
      httpStop?: (callId: string) => void;
    }
  ): {
    updatedDataHistory: CardContentType[];
    updatedMessageType: Record<number, boolean>;
    updatedRecommandQuestion: Array<{ index: number; subject: string }>;
    updatedNeedMarkBlueRunStepsArray: Array<any>;
    newLastDataItem: CardContentType;
    newIsFirst: boolean;
    updateCancel: boolean;
  } {
    const { 
      isFirst, 
      lastDataItem, 
      dataHistory, 
      messageType, 
      recommandQuestion, 
      needMarkBlueRunStepsArray,
      option, 
      isStop, 
      isCancel, 
      httpStop, 
    } = context;

    let currentIsFirst = isFirst;
    let currentLastDataItem = { ...lastDataItem };
    let currentDataHistory = [...dataHistory];
    let currentMessageType = { ...messageType };
    let currentRecommandQuestion = [...recommandQuestion];
    let currentNeedMarkBlueRunStepsArray = [...needMarkBlueRunStepsArray];  
    let currentCancel = isCancel;
    
    // 辅助函数：更新dataHistory中的tagData为全量的currentNeedMarkBlueRunStepsArray
    const updateTagDataForAllItems = () => {
      currentDataHistory.forEach((item) => {
        if(item.context && !item.isCard && !item.isIframe && !item.isKnowledgeData) {
          item.tagData = currentNeedMarkBlueRunStepsArray;
        }
      });
    };
    if (typeof data.data === 'string') {
      data.data = JSON.parse(data.data);
    }

    const sourceData = data.data;

    // 如果已停止，则尝试中断请求
    if (isStop) {
      const currentCallId = sourceData.callId;
      if (httpStop && currentCallId && !currentCancel) {
        httpStop(currentCallId);
        currentCancel = true;
      }
      return {
        updatedDataHistory: currentDataHistory,
        updatedMessageType: currentMessageType,
        updatedRecommandQuestion: currentRecommandQuestion,
        updatedNeedMarkBlueRunStepsArray: currentNeedMarkBlueRunStepsArray,
        newLastDataItem: currentLastDataItem,
        newIsFirst: currentIsFirst,
        updateCancel: currentCancel
      };
    }

    const citationsJson = sourceData.citationsJson;
    if (Number(sourceData?.status) === 500) {
      option.eventHandel('onerror', data);
    }
    // 增加体验模式超出次数的判断
    if(Number(sourceData?.code) === 2){
      option.eventHandel('onExeededLimit', data);
    }

    if (currentIsFirst) {
      option.eventHandel('connecting', data);
      currentIsFirst = false;
    } else {
      if (Number(sourceData.sessionType) === 999) {
        option.eventHandel('illegal', sourceData.sessionType);
      }
      if (sourceData.id) {
        option.eventHandel('onmessageId', sourceData.id);
      }
      switch (sourceData.messageType) {
        case 1:
          if (!currentMessageType[sourceData.messageType]) {
            option.eventHandel("onInit", sourceData);
          } else {
            if (sourceData.content) {
              currentDataHistory = utils.formatContent(sourceData.content, currentDataHistory);
              if (citationsJson) {
                dealCitationsJson(citationsJson, currentDataHistory);
              }
              // 更新tagData为全量数据
              updateTagDataForAllItems();
              option.eventHandel('onmessage', [...currentDataHistory]);
            }
          }
 
          break;

        case 2:
          option.eventHandel("onExecute", sourceData);
          break;

        case 5:
          let content = sourceData.content;
          let contextStr = "";

          if (typeof content === 'string' && content) {
            try {
              content = JSON.parse(content);
              contextStr = content.choices[0].delta?.content || "";
            } catch (error) {
              console.error('Error parsing content:', error);
            }
          }

          if (contextStr) {
            const items = utils.setStreamString(contextStr, currentLastDataItem);
            currentLastDataItem = items[items.length - 1];

            if (items.length === 1 && currentLastDataItem.isCard) {
              if (currentLastDataItem.finish === undefined || currentLastDataItem.finish === 1) {
                currentLastDataItem.finish = 0;
                items.forEach((item) => {
                  currentDataHistory[item.index] = { ...item };
                });
                // 更新tagData为全量数据
                updateTagDataForAllItems();
                option.eventHandel('onmessage', [...currentDataHistory]);
              }
            } else {
              items.forEach((item) => {
                currentDataHistory[item.index] = { ...item };
              });
              // 更新tagData为全量数据
              updateTagDataForAllItems();
              option.eventHandel('onmessage', [...currentDataHistory]);
            }
          }

          if (citationsJson) {
            dealCitationsJson(citationsJson, currentDataHistory);
          }

          // 更新tagData为全量数据
          updateTagDataForAllItems();
          option.eventHandel('onmessage', [...currentDataHistory]);
          break;

        case 6:
          if (sourceData.content && sourceData.content.startsWith("<questions>")) {
            const regex = /<question>(.*?)<\/question>/g;
            const matches = sourceData.content.match(regex);

            if (matches) {
              matches.forEach((match: string) => {
                const question = match.replace(/<question>|<\/question>/g, '');
                currentRecommandQuestion.push({
                  index: currentRecommandQuestion.length,
                  subject: question,
                });
              });
            }
          }
          break;
        case 7:
          if (sourceData.content) {
            const  knowledgeData= utils.dealKnowledgeData(sourceData.content);
            option.eventHandel('onknowledge', knowledgeData);
          }
          break;
        case 8:
          if (sourceData.content) {
            const markerData = utils.dealMarkerData(sourceData.content);
            console.log(markerData, 'markerData')
            let filterMarkerData = markerData.filter((item:any)=>{ return item.type.toLowerCase() === 'member'});
            currentNeedMarkBlueRunStepsArray = currentNeedMarkBlueRunStepsArray.concat(filterMarkerData);
            console.log(currentNeedMarkBlueRunStepsArray, 'currentNeedMarkBlueRunStepsArray')
            // 更新tagData为全量数据
            updateTagDataForAllItems();
            option.eventHandel('onmessage', [...currentDataHistory]);
          }
        break;
        default:
          break;
      }

      currentMessageType[sourceData.messageType] = true;

      if (sourceData.finish === 1) {
        if (currentDataHistory.length) {
          const lastItem = currentDataHistory[currentDataHistory.length - 1];
          lastItem.finish = 1;
          currentDataHistory[currentDataHistory.length - 1] = { ...lastItem };
          // 更新tagData为全量数据
          updateTagDataForAllItems();
          option.eventHandel('onmessage', [...currentDataHistory]);
        }

        if (currentRecommandQuestion.length) {
          option.eventHandel('onquestion', currentRecommandQuestion);
        }

        option.eventHandel('onclose', null);
      }
    }

    return {
      updatedDataHistory: currentDataHistory,
      updatedMessageType: currentMessageType,
      updatedRecommandQuestion: currentRecommandQuestion,
      updatedNeedMarkBlueRunStepsArray: currentNeedMarkBlueRunStepsArray,
      newLastDataItem: currentLastDataItem,
      newIsFirst: currentIsFirst,
      updateCancel: currentCancel
    };
  }
};

export const { setStreamString, parseString, getNewToken, formatContent, formatHistoryContent, handleStreamMessage, dealKnowledgeData, dealMarkerData } = utils;
