import requests from '@/api/requests';
import { openWebView } from '@/plugins/app-plugin';
import { aiToast } from '@/plugins/ai-toast';
import { getDeviceInfo } from '@/utils/common';
import {ICON_MAP} from "@/const/const";

export async function handleKnowledgeClick(item: any) {
  const { isApp } = getDeviceInfo();
  const appType = Number(item.appType);
  if ( appType === 0) { //非v5知识源
    const params = {
      responseType: 'blob',
    };
    const {data} = await requests.getShortToken();
    if (isApp) {
      const url = `${location.origin}/comi/ai-manager/repository/export/download/file/${item.resourceId}?token=${data}`;
      window.location.href = url;
    } else {
      requests.downloadFile(item.resourceId, data, params).then(res => {
        const blob = new Blob([res]);
        const url = URL.createObjectURL(blob);
        // downloadFile({url: url});
        const link = document.createElement('a');
        link.download = item.title;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(link);
        aiToast({
          content: '下载完成',
        });
      });
    }
  } else if(!ICON_MAP[appType as keyof typeof ICON_MAP] || appType === 5){
    aiToast({
      content: 'CoMi APP暂不支持查看该详情，请通过PC端尝试',
    })
  } else {
    jumpToTarget(item);
  }
}

export function jumpToTarget(item: any, fromToDo = false) {
  let extendProperty = null;
  if(item.extendProperty) {
    extendProperty = JSON.parse(item.extendProperty);
  }
  const {entityId, appType} = item;
  let jumpParams: any = {
    id: item.id,
    name: item.title,
    icon: '',
    imgSrc: null,
    parentId: '',
    target: 'newWindow',
    href: '',
    appId: appType,
    openApi: 'openApp',
    params: {
      id: entityId,
      comeFrom: 0,
    },
    properties: {
      branch: false,
    },
  };
  if(appType === 2) {
    if(extendProperty?.formType === 'cap4UnflowForm') { //无流程表单
      jumpParams.appId = 66;
    }else {
      jumpParams.appId = 1;
    }
    
  }
  switch (jumpParams.appId) {
    case 1: //协同 完成
      if(fromToDo){
        jumpParams.params.type = 'todo';
        jumpParams.params.option = {
          affairId: item.affairId,
        }
      }else {
        jumpParams.openApi = 'jumpToColSummary';
      }
      break;
    case 3: //文档 完成
      jumpParams.params.option = {
        id: entityId
      }
      break;
    // case 4: //公文 完成
    //   break;
    case 6: //会议 完成
      jumpParams.params.type = 'todo';
      jumpParams.params.option = {
        id: entityId,
        openFrom: 'glwd'
      }
      break;
    case 8: //新闻 完成
      jumpParams.openApi = 'jumpToNews';
      break;
    case 7: //公告 完成
      jumpParams.openApi = 'jumpToBulletin';
      break;
    case 9: //讨论 完成
      jumpParams.params.type = 'message';
      jumpParams.params.option = {
        id: entityId,
      }
      break;
    case 10: //调查 完成
      if(fromToDo) {
        jumpParams.params.type = 'todo';
        jumpParams.params.option = JSON.parse(item.gotoParams);
      }else {
        jumpParams.params.type = 'message';
        jumpParams.params.option = {
          id: entityId,
        }
      }
      break;
    case 11: //日程
      jumpParams.params.type = 'todo';
      jumpParams.params.option = {
        id: entityId,
        openFrom: 'index'
      }
      break;
    case 30: //任务管理
      jumpParams.params.type = 'message';
      jumpParams.params.option = {
        id: entityId,
      }
      break;
    case 70: //报表 
      const clientType = extendProperty?.clientType;
      if(clientType === '1') {
        aiToast({
          content: `${item.title}是PC端报表,请在PC端查看!` //移动端全文检索的提示
        });
        return;
      }
      jumpParams.openApi = 'reportView';
      jumpParams.from = null;
      jumpParams.params.rptInfo = JSON.parse(extendProperty.vReportParams);
      break;
    case 94: //领导行程
      jumpParams.openApi = 'appCreatePage';
      jumpParams.params = {
        agendaId: entityId
      };
      break;
    case 127: //协同邮箱
      // const boxTypeMap = {
      //   141: 1,
      //   142: 2,
      //   143: 3,
      //   144: 4,
      // }
      // const boxType = boxTypeMap[jumpParams.appId as keyof typeof boxTypeMap]
      // jumpParams.openApi = 'contact';
      // break;
  }
  const params = {
    url: `/pages/native/native.html?openType=normal&type=center`,
    screenOrientation: 'portrait',
    openType: 'normal',
    webviewBg: 'webviewLight',
    webviewBgRgb: "#FFFFFF",
    showTitle: true,
    adaptBottomSafe: true
  }
  if(jumpParams.appId === 4 ){
    if(fromToDo){
      jumpParams.params.type = 'todo';
      jumpParams.params.option = {
        affairId: item.affairId
      }
      jumpAction(jumpParams, params);
    }else {
      requests.getEdocAffairId(entityId).then(data => {
        if(Number(data.code) === 0) {
          const result = data.data;
          jumpParams.params.option = {
            affairId: result.linkId,
            summaryId: entityId,
            openFrom: 'glwd'
          }
          jumpAction(jumpParams, params);
        }
      });
    }
  }else{
    jumpAction(jumpParams, params);
  }
}

function jumpAction(jumpParams:object, params:object) {
  localStorage.setItem('jumpParams', JSON.stringify(jumpParams));
  localStorage.setItem('jumpPage', 'true');
  openWebView(params);
}